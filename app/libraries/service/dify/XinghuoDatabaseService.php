<?php

namespace app\libraries\service\dify;

use Cls\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use WebSocket\Client as WebSocketClient;

/**
 * 星火数据库服务
 * 接口文档：https://www.xfyun.cn/doc/spark/ChatDoc-API.html
 */
class XinghuoDatabaseService
{
    private Client $httpClient;
    private string $baseUrl = 'https://chatdoc.xfyun.cn/openapi';
    private string $appId;
    private string $secret;

    private static ?XinghuoDatabaseService $instance = null;

    public static function getInstance(): XinghuoDatabaseService
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function __construct()
    {
        $this->httpClient = new Client([
            'verify' => false, // 禁用SSL验证（生产环境不推荐）
        ]);
        $this->appId = config('chatgpt.xinghuo_database_appid');
        $this->secret = config('chatgpt.xinghu_database_secret');
    }

    /**
     * 获取签名
     * @return array 返回签名信息
     */
    private function getSignature(): array
    {
        try {
            $timestamp = time();
            $auth = md5($this->appId . $timestamp);
            $sign = base64_encode(hash_hmac('sha1', $auth, $this->secret, true));

            return [$this->appId, $timestamp, $sign];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 准备请求头
     * @return array
     */
    private function prepareHeaders(): array
    {
        [$appId, $timestamp, $sign] = $this->getSignature();

        return [
            'appId' => $appId,
            'timestamp' => $timestamp,
            'signature' => $sign,
        ];
    }

    /**
     * 上传文档
     * @param string $fileUrl 文件url
     * @return mixed
     * @throws \Exception
     */
    public function uploadFile(string $fileUrl)
    {
        $fileName = basename($fileUrl);
        $postData = [
            'url' => $fileUrl,
            'fileName' => $fileName,
            'fileType' => 'wiki',
            'parseType' => 'AUTO',
            //'parseType' => 'OCR',
        ];

        try {
            $response = $this->httpClient->post("{$this->baseUrl}/v1/file/upload", [
                'headers' => $this->prepareHeaders(),
                'form_params' => $postData,
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] !== 0) {
                LogError('xinghuo', '上传失败', '上传失败 ', [
                    'url' => $fileUrl,
                    'result' => $result
                ]);
                throw new \Exception('上传失败: ' . ($result['desc'] ?? '未知错误'));
            }

            return $result['data'];
        } catch (RequestException $e) {
            throw new \Exception('文件上传请求失败: ' . $e->getMessage());
        }
    }

    /**
     * 文档状态查询
     * @param string $fileId
     * @return mixed
     * @throws \Exception
     */
    public function fileStatusQuery($fileId)
    {
        $postData = [
            'fileIds' => $fileId,
        ];

        try {
            $response = $this->httpClient->post("{$this->baseUrl}/v1/file/status", [
                'headers' => $this->prepareHeaders(),
                'form_params' => $postData,
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] !== 0) {
                throw new \Exception('查询失败: ' . ($result['msg'] ?? '未知错误'));
            }

            return $result['data'][0];
        } catch (RequestException $e) {
            throw new \Exception('文件状态查询失败: ' . $e->getMessage());
        }
    }

    /**
     * 文档问答
     * @param string $fileId
     * @param string $question
     * @return string
     * @throws \Exception
     */
    public function fileQA($fileId, $question)
    {
        [$appId, $timestamp, $sign] = $this->getSignature();
        $authUrl = "wss://chatdoc.xfyun.cn/openapi/chat?appId={$appId}&timestamp={$timestamp}&signature={$sign}";

        try {
            $client = new WebSocketClient($authUrl);

            $data = json_encode([
                'fileIds' => [$fileId],
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $question
                    ]
                ],
                'chatExtends' => [
                    'wikiPromptTpl' => "请将以下内容作为已知信息：\n<wikicontent>\n请根据以上内容回答用户的问题。\n问题:<wikiquestion>\n回答:",
                    'wikiFilterScore' => 0.82,
                    'temperature' => 0.5,
                    'spark' => false,
                ]
            ]);

            $client->send($data);

            $answer = "";
            while (true) {
                $response = $client->receive();
                $receiveData = json_decode($response, true);

                if (is_array($receiveData) && $receiveData['code'] == 0 && !empty($receiveData['content'])) {
                    $answer .= $receiveData['content'];
                }

                if (!$receiveData) {
                    $client->close();
                    return $answer;
                }
            }
        } catch (\Exception $e) {
            throw new \Exception('文档问答连接失败: ' . $e->getMessage());
        }
    }

    /**
     * 文档相似内容搜索
     * @param $fileId
     * @param $question
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function fileSimilarSearch($fileId, $question)
    {
        $postData = [
            'fileIds' => [$fileId],
            'topN' => 5,
            'content' => $question,
        ];

        try {
            $response = $this->httpClient->post("{$this->baseUrl}/v1/vector/search", [
                'headers' => $this->prepareHeaders(),
                'json' => $postData,      // 注意 这个接口的参数是json格式
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] !== 0) {
                throw new \Exception('查询失败: ' . ($result['msg'] ?? '未知错误'));
            }

            return $result['data'];
        } catch (RequestException $e) {
            throw new \Exception('文档相似内容搜索失败: ' . $e->getMessage());
        }
    }

    /**
     * 文档分块内容获取
     * @param $fileId
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function fileChunkLists($fileId)
    {
        $postData = [
            'fileId' => $fileId,
        ];

        try {
            $response = $this->httpClient->post("{$this->baseUrl}/v1/file/chunks", [
                'headers' => $this->prepareHeaders(),
                'form_params' => $postData,
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] !== 0) {
                throw new \Exception('查询失败: ' . ($result['msg'] ?? '未知错误'));
            }

            return $result['data'];
        } catch (RequestException $e) {
            throw new \Exception('文档分块内容获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 文档列表
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function fileLists()
    {
        try {
            $response = $this->httpClient->get("{$this->baseUrl}/v1/file/list", [
                'headers' => $this->prepareHeaders(),
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['code'] !== 0) {
                throw new \Exception('查询失败: ' . ($result['msg'] ?? '未知错误'));
            }

            return $result['data'];
        } catch (RequestException $e) {
            throw new \Exception('文档列表获取失败: ' . $e->getMessage());
        }
    }
}
