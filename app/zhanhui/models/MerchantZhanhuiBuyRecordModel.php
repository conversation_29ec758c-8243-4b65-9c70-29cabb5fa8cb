<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property string $zhanhuiName 展会名称
 * @property int $platformUserSysId 用户id
 * @property string $orderNo 订单编号
 * @property string $orderStatus 订单状态：wait-等待执行；ready-准备完成；doing-正在执行；fail-执行失败；success执行成功
 * @property int $payStatus 支付状态：1-未支付；2-已支付
 * @property int $payAmount 支付金额，单位分
 * @property string $packageGuid 套餐GUID
 * @property string $packageInfo 套餐信息JSON
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiBuyRecordModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui_buy_record';

    protected static bool $isGuid = true;

    protected $json = ['packageInfo'];

    // 订单执行状态
    public const ORDER_STATUS_WAIT = 'wait'; // 订单状态：wait-等待执行
    public const ORDER_STATUS_READY = 'ready'; // 订单状态：ready-准备完成
    public const ORDER_STATUS_DOING = 'doing'; // 订单状态：doing-正在执行
    public const ORDER_STATUS_FAIL = 'fail'; // 订单状态：fail-执行失败
    public const ORDER_STATUS_SUCCESS = 'success'; // 订单状态：success执行成功

    // 订单支付状态
    public const PAY_STATUS_UNPAID = 1; // 支付状态：1-未支付
    public const PAY_STATUS_PAID = 2; // 支付状态：2-已支付

    public function recordInfo()
    {
        return $this->hasOne(MerchantZhanhuiBuyRecordInfoModel::class, 'buy_record_order_no', 'order_no');
    }

    public function zhanhui()
    {
        return $this->hasOne(MerchantZhanhuiModel::class, 'guid', 'zhanhuiGuid');
    }
}
