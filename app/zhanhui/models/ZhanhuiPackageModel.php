<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $packageName 套餐名称
 * @property string $packageDescription 套餐描述
 * @property int $packagePrice 套餐价格，单位分
 * @property int $validityPeriod 套餐有效期，天数
 * @property int $initialAiPoints 套餐初始AI点数
 * @property int $documentSupplements 套餐文档补充次数
 * @property int $status 上架状态：1-上架，0-下架
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class ZhanhuiPackageModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'zhanhui_package';
    protected static bool $isGuid = true;

    // 上架状态
    public const STATUS_ONLINE = 1; // 上架
    public const STATUS_OFFLINE = 0; // 下架
}
