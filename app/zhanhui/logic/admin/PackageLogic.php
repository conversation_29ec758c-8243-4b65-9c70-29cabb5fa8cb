<?php

namespace app\zhanhui\logic\admin;

use app\constdir\SysErrorCode;
use app\zhanhui\models\ZhanhuiPackageModel;

class PackageLogic
{
    /**
     * 创建套餐
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function create(array $params): array
    {
        $package = new ZhanhuiPackageModel();
        $package->packageName = $params['packageName'];
        $package->packageDescription = $params['packageDescription'];
        $package->packagePrice = yuan_to_fen($params['packagePrice']);
        $package->validityPeriod = $params['validityPeriod'];
        $package->initialAiPoints = $params['initialAiPoints'];
        $package->documentSupplements = $params['documentSupplements'];
        $package->status = $params['status'];
        $package->save();

        return [
            'guid' => $package->guid
        ];
    }

    /**
     * 编辑套餐
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function update(array $params): array
    {
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        $package->packageName = $params['packageName'];
        $package->packageDescription = $params['packageDescription'];
        $package->packagePrice = yuan_to_fen($params['packagePrice']);
        $package->validityPeriod = $params['validityPeriod'];
        $package->initialAiPoints = $params['initialAiPoints'];
        $package->documentSupplements = $params['documentSupplements'];
        $package->status = $params['status'];
        $package->save();

        return [];
    }

    /**
     * 删除套餐
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function delete(array $params): array
    {
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }
        $package->delete();;

        return [];
    }

    /**
     * 套餐列表
     * @param array $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function list(array $params): array
    {
        $query = ZhanhuiPackageModel::getInstance()
            ->where('deleted_at', 'null');

        // 状态筛选
        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        // 名称搜索
        if (!empty($params['packageName'])) {
            $query->where('package_name', 'like', '%' . $params['packageName'] . '%');
        }

        $result = $query->order('create_time', 'desc')
            ->paginate($params['pageSize'] ?? 10);

        $list = $result->toArray();
        
        // 格式化数据
        foreach ($list['data'] as &$item) {
            $item['packagePrice'] = fen_to_yuan($item['packagePrice']);
            $item['createTime'] = date('Y-m-d H:i:s', $item['createTime']);
            $item['updateTime'] = date('Y-m-d H:i:s', $item['updateTime']);
        }

        return $list;
    }

    /**
     * 套餐详情
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $params): array
    {
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $params['guid'])
            ->where('deleted_at', 'null')
            ->findOrEmpty();
        
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }

        $data = $package->toArray();
        $data['packagePrice'] = fen_to_yuan($data['packagePrice']);
        $data['createTime'] = date('Y-m-d H:i:s', $data['createTime']);
        $data['updateTime'] = date('Y-m-d H:i:s', $data['updateTime']);

        return $data;
    }

    /**
     * 上下架套餐
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function changeStatus(array $params): array
    {
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在');
        }
        if ($params['status'] == ZhanhuiPackageModel::STATUS_ONLINE) {
            $package->status = ZhanhuiPackageModel::STATUS_ONLINE;
        } else {
            $package->status = ZhanhuiPackageModel::STATUS_OFFLINE;
        }
        $package->save();

        return [];
    }
}