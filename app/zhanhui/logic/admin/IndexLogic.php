<?php

namespace app\zhanhui\logic\admin;

use app\admin\models\AdminUserModel;
use app\constdir\SysErrorCode;
use app\constdir\SysTime;
use app\libraries\service\token\TokenService;
use app\merchant\models\MerchantModel;
use app\user\models\UsersModel;
use app\zhanhui\cache\ZhanhuiCache;
use app\zhanhui\models\MerchantZhanhuiExchangeCodeModels;
use app\zhanhui\models\MerchantZhanhuiFaqModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiBannerModel;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use think\facade\Db;

class IndexLogic
{
    /**
     * 创建展会
     * @param $data
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function create($data)
    {
        $cacheKey = 'create_' . $data['merchantGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($cacheKey, 5);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '信息正在处理中，请稍后再试');
        }
        try {
            $this->saveZhanhuiInfo($data);
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            ZhanhuiCache::getInstance()->unlock($cacheKey, $lock);
        }

        return [];
    }

    /**
     * 更新展会
     * @param $data
     * @throws \app\libraries\exception\ApiException
     */
    public function update($data)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $data['guid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $this->saveZhanhuiInfo($data);
        return [];
    }

    /**
     * 展会信息保存
     * @param $data
     * @param mixed $zhanhuiInfo
     * @return void
     */
    private function saveZhanhuiInfo($data): void
    {
        MerchantZhanhuiModel::getInstance()->startTrans();
        try {
            if (!empty($data['guid'])) {
                $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
                    ->where('guid', $data['guid'])
                    ->findOrEmpty();
                if ($zhanhuiInfo->isEmpty()) {
                    throw new \Exception('展会不存在');
                }
            } else {
                $zhanhuiInfo = MerchantZhanhuiModel::getInstance();
            }
            $zhanhuiInfo->name = $data['name'];
            $zhanhuiInfo->merchantGuid = $data['merchantGuid'];
            $zhanhuiInfo->shortName = $data['shortName'] ?? '';
            $zhanhuiInfo->logo = $data['logo'];
            $zhanhuiInfo->slogo = $data['slogo'];
            $zhanhuiInfo->cover = $data['cover'];
            $zhanhuiInfo->description = $data['description'];
            $zhanhuiInfo->startTime = strtotime($data['startTime']);
            $zhanhuiInfo->endTime = strtotime($data['endTime']);
            $zhanhuiInfo->endIsShow = $data['endIsShow'];
            $zhanhuiInfo->showOrder = $data['showOrder'] ?? 1;
            $zhanhuiInfo->showTime = $data['showTime'] ?? 1;
            $zhanhuiInfo->save();

            // 判断展会配置是否存在
            $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
                ->where('zhanhui_guid', $zhanhuiInfo->guid)
                ->findOrEmpty();
            if ($zhanhuiConfigInfo->isEmpty()) {
                $zhanhuiConfigInfo->merchantGuid = $data['merchantGuid'];
                $zhanhuiConfigInfo->zhanhuiGuid = $zhanhuiInfo->guid;
                $zhanhuiConfigInfo->welcomeText = $data['welcomeText'];
                $zhanhuiConfigInfo->isRequirePhone = $data['isRequirePhone'];
                $zhanhuiConfigInfo->merchantKnowledgeGuid = $data['merchantKnowledgeGuid'] ?? '';
                $zhanhuiConfigInfo->knowledgePrompt = $data['knowledgePrompt'] ?? '';
                $zhanhuiConfigInfo->aiChatPrompt = $data['aiChatPrompt'] ?? '';
                $zhanhuiConfigInfo->aboutZhanhuiTitle = $data['aboutZhanhuiTitle'] ?? '';
                $zhanhuiConfigInfo->save();
            } else {
                $zhanhuiConfigInfo->welcomeText = $data['welcomeText'];
                $zhanhuiConfigInfo->isRequirePhone = $data['isRequirePhone'] ?? 2;
                if (!empty($data['merchantKnowledgeGuid'])) {
                    $zhanhuiConfigInfo->merchantKnowledgeGuid = $data['merchantKnowledgeGuid'];
                }
                $zhanhuiConfigInfo->knowledgePrompt = $data['knowledgePrompt'] ?? '';
                $zhanhuiConfigInfo->aiChatPrompt = $data['aiChatPrompt'] ?? '';
                $zhanhuiConfigInfo->aboutZhanhuiTitle = $data['aboutZhanhuiTitle'] ?? '';
                $zhanhuiConfigInfo->save();
            }
            MerchantZhanhuiModel::getInstance()->commit();
        } catch (\Exception $e) {
            MerchantZhanhuiModel::getInstance()->rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }


    /**
     * 展会列表
     * @param $params
     * @return mixed
     */
    public function lists($params)
    {
        $pageSize = $params['pageSize'] ?? 50;
        $showStatus = $params['showStatus'] ?? '';
        $tokenEntity = TokenService::getInstance()->getTokenEntity();
        $admin = AdminUserModel::getInstance()->find($tokenEntity->userId);
        if ($admin->platformUserSysId > 0) {
            $zhanhuiGuids = MerchantZhanhuiAdminUserModel::getInstance()
                ->where('merchant_guid', $params['merchantGuid'])
                ->where('platform_user_sys_id', $admin->platformUserSysId)
                ->column('zhanhui_guid');
            MerchantZhanhuiModel::getInstance()
                ->where('merchant_guid', $params['merchantGuid'])
                ->whereIn('guid', $zhanhuiGuids)
                ->where('platform_user_sys_id', 0)
                ->update([
                    'platform_user_sys_id' => $admin->platformUserSysId
                ]);
        }

        $query = MerchantZhanhuiModel::getInstance();
        $query = $query->where('merchant_guid', $params['merchantGuid'])->order('sys_id', 'desc');
        if ($showStatus !== '') {
            if ($showStatus) {
                $query = $query
                    ->where('status', MerchantZhanhuiModel::STATUS_NORMAL)
                    ->where(function ($query) use ($showStatus) {
                        $query->where('end_is_show', '<>', 0)->whereOr('end_time', '>=', time());
                    });
            } else {
                $query = $query->where(function ($query) {
                    $query->where('status', MerchantZhanhuiModel::STATUS_DISABLE)
                        ->whereOr(function ($query) {
                            $query->where('end_time', '<', time())->where('end_is_show', 0);
                        });
                });

            }
        }
        if ($admin->platformUserSysId > 0) {
            $query = $query->where('platform_user_sys_id', $admin->platformUserSysId);
        }
        $lists = $query->paginate($pageSize);
       // 处理展示数据
        foreach ($lists as $key => $value) {
            // 如果截止时间跨年，显示年份
            $endTime = $value['endTime'];
            if (date("Y", $value['startTime']) != date("Y", $endTime)) {
                $lists[$key]['endTime'] = date("Y-m-d", $endTime);
            } else {
                $lists[$key]['endTime'] = date("m-d", $endTime);
            }
            $lists[$key]['startTime'] = date("Y-m-d", $value['startTime']);
            // 是否展示
            $showStatus = true;
            $closeReason = '';
            if ($value['status'] == MerchantZhanhuiModel::STATUS_DISABLE) {
                $showStatus = false;
                $closeReason = '展会已手动禁用';
            } else {
                if (time() > $endTime && $value['endIsShow'] == 0) {
                    $showStatus = false;
                    $closeReason = '开启了展会结束不显示';
                }
            }
            $lists[$key]['showStatus'] = $showStatus;
            $lists[$key]['closeReason'] = $closeReason;
            $lists[$key]['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
        }
        return $lists->toArray();
    }

    /**
     * 展会详情
     * @param $params
     * @return mixed
     * @throws \app\libraries\exception\ApiException
     */
    public function detail($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $zhanhuiInfo->guid)
            ->findOrEmpty();
        if ($zhanhuiConfigInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会配置不存在');
        }
        $zhanhuiInfo['startTime'] = date("Y-m-d", $zhanhuiInfo['startTime']);
        $zhanhuiInfo['endTime'] = date("Y-m-d", $zhanhuiInfo['endTime']);
        $zhanhuiInfo['welcomeText'] = $zhanhuiConfigInfo['welcomeText'];
        $zhanhuiInfo['isRequirePhone'] = $zhanhuiConfigInfo['isRequirePhone'];
        $zhanhuiInfo['merchantKnowledgeGuid'] = $zhanhuiConfigInfo['merchantKnowledgeGuid'];
        $zhanhuiInfo['knowledgePrompt'] = $zhanhuiConfigInfo['knowledgePrompt'];
        $zhanhuiInfo['aiChatPrompt'] = $zhanhuiConfigInfo['aiChatPrompt'];
        $zhanhuiInfo['topicBannerTitle'] = $zhanhuiConfigInfo['topicBannerTitle'];
        $zhanhuiInfo['guestBannerTitle'] = $zhanhuiConfigInfo['guestBannerTitle'];
        return $zhanhuiInfo->toArray();
    }


    /**
     * 删除展会
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function delete($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $zhanhuiInfo->delete();
        MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $params['guid'])
            ->delete();

        return [];
    }

    /**
     * 更改展会状态
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function changeStatus($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        if ($zhanhuiInfo->status == MerchantZhanhuiModel::STATUS_NORMAL) {
            $zhanhuiInfo->status = MerchantZhanhuiModel::STATUS_DISABLE;
        } else {
            $zhanhuiInfo->status = MerchantZhanhuiModel::STATUS_NORMAL;
        }
        $zhanhuiInfo->save();
        return [];
    }


    /**
     *  展会点数充值
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function rechargePoint($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $zhanhuiInfo->aiPoint = Db::raw('ai_point+' . $params['rechargePoint']);
        $zhanhuiInfo->save();
        return [];
    }

    /**
     * 创建常见问题
     * @param $params
     * @return array
     */
    public function faqCreate($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $faqInfo = new MerchantZhanhuiFaqModel();
        $faqInfo->merchantGuid = $zhanhuiInfo->merchantGuid;
        $faqInfo->zhanhuiGuid = $params['zhanhuiGuid'];
        $faqInfo->question = $params['question'];
        $faqInfo->aiChatQuestion = $params['aiChatQuestion'];
        $faqInfo->answerType = $params['answerType'];
        $faqInfo->answer = $params['answer'];
        $faqInfo->position = $params['position'];
        $faqInfo->iconType = $params['iconType'];
        $faqInfo->sort = $params['sort'];
        $faqInfo->imageList = $params['imageList'] ?? [];
        $faqInfo->save();
        return [];
    }

    /**
     * 更新常见问题
     * @param $params
     * @return array
     */
    public function faqUpdate($params)
    {
        $faqInfo = MerchantZhanhuiFaqModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($faqInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '常见问题不存在');
        }
        $faqInfo->question = $params['question'];
        $faqInfo->aiChatQuestion = $params['aiChatQuestion'];
        $faqInfo->answerType = $params['answerType'];
        $faqInfo->answer = $params['answer'];
        $faqInfo->position = $params['position'];
        $faqInfo->iconType = $params['iconType'];
        $faqInfo->sort = $params['sort'];
        $faqInfo->imageList = $params['imageList'] ?? [];
        $faqInfo->save();
        return [];
    }

    /**
     * 删除常见问题
     * @param $params
     * @return array
     */
    public function faqDelete($params)
    {
        $faqInfo = MerchantZhanhuiFaqModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($faqInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '常见问题不存在');
        }
        $faqInfo->delete();
        return [];
    }

    /**
     * 常见问题详情
     * @param $params
     * @return mixed
     */
    public function faqDetail($params)
    {
        $faqInfo = MerchantZhanhuiFaqModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($faqInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '常见问题不存在');
        }
        return $faqInfo->toArray();
    }

    /**
     * 常见问题列表
     * @param $params
     * @return mixed
     */
    public function faqLists($params)
    {
        $pageSize = $params['pageSize'] ?? 10;
        $query = MerchantZhanhuiFaqModel::getInstance()->where('zhanhui_guid', $params['zhanhuiGuid']);
        if (!empty($params['position'])) {
            $query = $query->where('position', $params['position']);
        }
        $lists = $query->order('sort', 'asc')->paginate($pageSize);
        foreach ($lists as $key => $value) {
            $lists[$key]['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
        }
        return $lists->toArray();
    }

    /**
     * 轮播图创建
     * @param $params
     * @return array
     */
    public function bannerCreate($params)
    {
        $zhahuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhahuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $bannerModel = new MerchantZhanhuiBannerModel();
        $bannerModel->merchantGuid = $zhahuiInfo->merchantGuid;
        $bannerModel->zhanhuiGuid = $params['zhanhuiGuid'];
        $bannerModel->bannerType = $params['bannerType'];
        $bannerModel->title = $params['title'];
        $bannerModel->content = $params['content'];
        $bannerModel->image = $params['image'];
        $bannerModel->jumpType = $params['jumpType'];
        $bannerModel->jumpValue = $params['jumpValue'];
        $bannerModel->sort = $params['sort'];
        $bannerModel->save();

        return [];
    }

    /**
     * 轮播图修改
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function bannerUpdate($params)
    {
        $bannerModel = MerchantZhanhuiBannerModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($bannerModel->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '轮播图不存在');
        }
        $bannerModel->bannerType = $params['bannerType'];
        $bannerModel->title = $params['title'];
        $bannerModel->content = $params['content'];
        $bannerModel->image = $params['image'];
        $bannerModel->jumpType = $params['jumpType'];
        $bannerModel->jumpValue = $params['jumpValue'];
        $bannerModel->sort = $params['sort'];
        $bannerModel->save();
        return [];
    }

    /**
     * 轮播图删除
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function bannerDelete($params)
    {
        $bannerModel = MerchantZhanhuiBannerModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($bannerModel->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '轮播图不存在');
        }
        $bannerModel->delete();
        return [];
    }

    /**
     * 轮播图列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function bannerLists($params)
    {
        $pageSize = $params['pageSize'] ?? 10;
        $query = MerchantZhanhuiBannerModel::getInstance()->where('zhanhui_guid', $params['zhanhuiGuid']);
        if (!empty($params['bannerType'])) {
            $query = $query->where('banner_type', $params['bannerType']);
        }
        $lists = $query->order('sort', 'asc')->paginate($pageSize);
        foreach ($lists as $key => $value) {
            $lists[$key]['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
        }
        return $lists->toArray();
    }

    /**
     * 轮播图自定义标题修改
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function bannerTitleUpdate($params)
    {
        $zhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiConfigInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会配置不存在');
        }
        if ($params['bannerType'] == MerchantZhanhuiBannerModel::BANNER_TYPE_YITI) {
            $zhanhuiConfigInfo->topicBannerTitle = $params['title'];
        } elseif ($params['bannerType'] == MerchantZhanhuiBannerModel::BANNER_TYPE_GUEST) {
            $zhanhuiConfigInfo->guestBannerTitle = $params['title'];
        } elseif ($params['bannerType'] == MerchantZhanhuiBannerModel::BANNER_TYPE_COMPANY) {
            $zhanhuiConfigInfo->recommendCompanyTitle = $params['title'];
        } else {
            throwException(SysErrorCode::SYS_ERROR_CODE, '轮播图类型错误');
        }
        $zhanhuiConfigInfo->save();

        return [];
    }

    /**
     * 展会管理员列表
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function zhanhuiAdminUsers($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $adminUsers = MerchantZhanhuiAdminUserModel::getInstance()
            ->with(['user'])
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->select();
        return $adminUsers->toArray();
    }

    /**
     * 展会管理员创建
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function zhanhuiAdminUserCreate($params)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }
        $userInfo = UsersModel::getInstance()
            ->where('guid', $params['userGuid'])
            ->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->where('platform_user_sys_id', $userInfo['sys_id'])
            ->findOrEmpty();
        if (!$adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '管理员已存在');
        }
        $adminUser->merchantGuid = $zhanhuiInfo['merchant_guid'];
        $adminUser->zhanhuiGuid = $params['zhanhuiGuid'];
        $adminUser->platformUserSysId = $userInfo['sys_id'];
        $adminUser->save();
        return [];
    }


    /**
     * 展会管理员删除
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function zhanhuiAdminUserDelete($params)
    {
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('guid', $params['adminGuid'])
            ->findOrEmpty();
        if ($adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '管理员不存在');
        }
        $adminUser->delete();
        return [];
    }

    /**
     * 展会管理员修改
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function zhanhuiAdminUserUpdate($params)
    {
        $adminUser = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('guid', $params['adminGuid'])
            ->findOrEmpty();
        if ($adminUser->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '管理员不存在');
        }

        // 只修改isSuperAdmin字段
        if (isset($params['isSuperAdmin'])) {
            $adminUser->isSuperAdmin = (int)$params['isSuperAdmin'];
        }

        $adminUser->save();
        return [];
    }

    /**
     * 复制展会
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function copy($params)
    {
        // 获取原展会信息
        $sourceZhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($sourceZhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        if (!isset($params['platformUserSysId'])) {
            $tokenEntity = TokenService::getInstance()->getTokenEntity();
            $admin = AdminUserModel::getInstance()->find($tokenEntity->userId);
            if ($admin->platformUserSysId != $sourceZhanhuiInfo->platformUserSysId) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '无展位复制权限');
            }
        }

        // 获取原展会配置信息
        $sourceZhanhuiConfigInfo = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $sourceZhanhuiInfo->guid)
            ->findOrEmpty();

        $platformUserSysId = $params['platformUserSysId'] ?? $sourceZhanhuiInfo->platformUserSysId;

        // 开始事务
        MerchantZhanhuiModel::getInstance()->startTrans();
        try {
            // 1. 复制展会基础信息
            $newZhanhuiInfo = new MerchantZhanhuiModel();
            $newZhanhuiInfo->merchantGuid = $params['merchantGuid'] ?? $sourceZhanhuiInfo->merchantGuid; // 支持指定新的商户GUID
            $newZhanhuiInfo->name = $sourceZhanhuiInfo->name . '(复制)';
            $newZhanhuiInfo->shortName = $sourceZhanhuiInfo->shortName . '(复制)';
            $newZhanhuiInfo->logo = $sourceZhanhuiInfo->logo;
            $newZhanhuiInfo->slogo = $sourceZhanhuiInfo->slogo;
            $newZhanhuiInfo->cover = $sourceZhanhuiInfo->cover;
            $newZhanhuiInfo->description = $sourceZhanhuiInfo->description;
            $newZhanhuiInfo->startTime = $sourceZhanhuiInfo->startTime;
            $newZhanhuiInfo->endTime = $sourceZhanhuiInfo->endTime;
            $newZhanhuiInfo->endIsShow = $sourceZhanhuiInfo->endIsShow;
            $newZhanhuiInfo->aiPoint = 10000;
            $newZhanhuiInfo->status = $sourceZhanhuiInfo->status;
            $newZhanhuiInfo->showOrder = $sourceZhanhuiInfo->showOrder;
            $newZhanhuiInfo->showTime = $sourceZhanhuiInfo->showTime;
            $newZhanhuiInfo->createStatus = $sourceZhanhuiInfo->createStatus;
            $newZhanhuiInfo->platformUserSysId = $platformUserSysId;
            $newZhanhuiInfo->save();

            // 2. 复制展会配置信息（不复制知识库GUID）
            if (!$sourceZhanhuiConfigInfo->isEmpty()) {
                $newZhanhuiConfigInfo = new MerchantZhanhuiConfigModel();
                $newZhanhuiConfigInfo->merchantGuid = $newZhanhuiInfo->merchantGuid;
                $newZhanhuiConfigInfo->zhanhuiGuid = $newZhanhuiInfo->guid;
                $newZhanhuiConfigInfo->topicBannerTitle = $sourceZhanhuiConfigInfo->topicBannerTitle;
                $newZhanhuiConfigInfo->guestBannerTitle = $sourceZhanhuiConfigInfo->guestBannerTitle;
                $newZhanhuiConfigInfo->recommendCompanyTitle = $sourceZhanhuiConfigInfo->recommendCompanyTitle;
                $newZhanhuiConfigInfo->welcomeText = $sourceZhanhuiConfigInfo->welcomeText;
                $newZhanhuiConfigInfo->aboutZhanhuiTitle = $sourceZhanhuiConfigInfo->aboutZhanhuiTitle;
                $newZhanhuiConfigInfo->isRequirePhone = $sourceZhanhuiConfigInfo->isRequirePhone;
                // 不复制知识库GUID
                $newZhanhuiConfigInfo->merchantKnowledgeGuid = '';
                $newZhanhuiConfigInfo->knowledgePrompt = $sourceZhanhuiConfigInfo->knowledgePrompt;
                $newZhanhuiConfigInfo->aiChatPrompt = $sourceZhanhuiConfigInfo->aiChatPrompt;
                $newZhanhuiConfigInfo->save();
            } else {
                // 如果原展会没有配置信息，创建一个基本的配置
                $newZhanhuiConfigInfo = new MerchantZhanhuiConfigModel();
                $newZhanhuiConfigInfo->merchantGuid = $newZhanhuiInfo->merchantGuid;
                $newZhanhuiConfigInfo->zhanhuiGuid = $newZhanhuiInfo->guid;
                $newZhanhuiConfigInfo->save();
            }

            // 3. 复制展会常见问题
            $sourceFaqList = MerchantZhanhuiFaqModel::getInstance()
                ->where('zhanhui_guid', $sourceZhanhuiInfo->guid)
                ->select();
            // 常见问题批量创建，500个为一组创建
            $faqData = [];
            foreach ($sourceFaqList as $faqInfo) {
                $faqData[] = [
                    'guid' => get_guid(),
                    'merchant_guid' => $newZhanhuiInfo->merchantGuid,
                    'zhanhui_guid' => $newZhanhuiInfo->guid,
                    'question' => $faqInfo->question,
                    'ai_chat_question' => $faqInfo->aiChatQuestion,
                    'answer_type' => $faqInfo->answerType,
                    'answer' => $faqInfo->answer,
                    'position' => $faqInfo->position,
                    'icon_type' => $faqInfo->iconType,
                    'sort' => $faqInfo->sort,
                    'image_list' => $faqInfo->imageList,
                    'create_time' => time(),
                ];
                if (count($faqData) == 500) {
                    $faqModel = new MerchantZhanhuiFaqModel();
                    $faqModel->saveAll($faqData);
                    $faqData = [];
                }
            }
            if (!empty($faqData)) {
                $faqModel = new MerchantZhanhuiFaqModel();
                $faqModel->saveAll($faqData);
            }

            // 4. 复制展会banner
            $sourceBannerList = MerchantZhanhuiBannerModel::getInstance()
                ->where('zhanhui_guid', $sourceZhanhuiInfo->guid)
                ->select();
            foreach ($sourceBannerList as $bannerInfo) {
                $newBannerInfo = new MerchantZhanhuiBannerModel();
                $newBannerInfo->merchantGuid = $newZhanhuiInfo->merchantGuid;
                $newBannerInfo->zhanhuiGuid = $newZhanhuiInfo->guid;
                $newBannerInfo->bannerType = $bannerInfo->bannerType;
                $newBannerInfo->title = $bannerInfo->title;
                $newBannerInfo->content = $bannerInfo->content;
                $newBannerInfo->image = $bannerInfo->image;
                $newBannerInfo->jumpType = $bannerInfo->jumpType;
                $newBannerInfo->jumpValue = $bannerInfo->jumpValue;
                $newBannerInfo->sort = $bannerInfo->sort;
                $newBannerInfo->save();
            }

            // 展会管理员
            if (isset($params['platformUserSysId'])) {
                $newAdminInfo = new MerchantZhanhuiAdminUserModel();
                $newAdminInfo->merchantGuid = $newZhanhuiInfo->merchantGuid;
                $newAdminInfo->zhanhuiGuid = $newZhanhuiInfo->guid;
                $newAdminInfo->platformUserSysId = $platformUserSysId;
                $newAdminInfo->isSuperAdmin = MerchantZhanhuiAdminUserModel::IS_NOT_SUPER_ADMIN;
                $newAdminInfo->save();
            }

            MerchantZhanhuiModel::getInstance()->commit();
            return ['guid' => $newZhanhuiInfo->guid];
        } catch (\Exception $e) {
            MerchantZhanhuiModel::getInstance()->rollback();
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        }
    }

    /**
     * 兑换码创建
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function exchangeCodeCreate($params)
    {
        $exist = MerchantZhanhuiExchangeCodeModels::getInstance()
            ->where('exchange_code', $params['exchangeCode'])
            ->findOrEmpty();
        if (!$exist->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '该兑换码已存在，请换一个');
        }
        $exchangeCodeModel = new MerchantZhanhuiExchangeCodeModels();
        $exchangeCodeModel->merchantGuid = $params['merchantGuid'];
        $exchangeCodeModel->exchangeCode = $params['exchangeCode'];
        $exchangeCodeModel->save();
        return [];
    }

    /**
     * 兑换码列表
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function exchangeCodeLists($params)
    {
        $pageSize = $params['pageSize'] ?? 10;
        $query = MerchantZhanhuiExchangeCodeModels::getInstance();
        if (!empty($params['merchantGuid'])) {
            $query = $query->where('merchant_guid', $params['merchantGuid']);
        }
        if (!empty($params['useStatus'])) {
            $query = $query->where('exchange_status', $params['useStatus']);
        }
        $lists = $query->order('sys_id', 'desc')->paginate($pageSize);
        $lists =  $lists->toArray();
        $merchantName = MerchantModel::getInstance()
            ->whereIn('guid', array_column($lists['data'], 'merchantGuid'))
            ->column('merchant_name','guid');
        foreach ($lists['data'] as $key => $value) {
            $lists['data'][$key]['merchantName'] = $merchantName[$value['merchantGuid']] ?? '';
            $lists['data'][$key]['createTime'] = date(SysTime::COMMAND_LOG_FORMAT, $value['createTime']);
        }
        return $lists;
    }

    /**
     * 兑换码删除
     * @param $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function exchangeCodeDelete($params)
    {
        $exchangeCodeModel = MerchantZhanhuiExchangeCodeModels::getInstance()
            ->where('guid', $params['guid'])
            ->findOrEmpty();
        if ($exchangeCodeModel->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '兑换码不存在');
        }
        $exchangeCodeModel->delete();
        return [];
    }
}
