<?php

namespace app\zhanhui\logic\api;

use app\constdir\SysErrorCode;
use app\zhanhui\models\ZhanhuiPackageModel;

class PackageLogic
{
    /**
     * 套餐列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(): array
    {
        $packages = ZhanhuiPackageModel::getInstance()
            ->where('status', ZhanhuiPackageModel::STATUS_ONLINE)
            ->order('create_time', 'desc')
            ->select();
        $list = [];
        foreach ($packages as $package) {
            $list[] = [
                'guid' => $package->guid,
                'packageName' => $package->packageName,
                'packageDescription' => $package->packageDescription,
                'packagePrice' => fen_to_yuan($package->packagePrice),
                'validityPeriod' => $package->validityPeriod,
                'initialAiPoints' => $package->initialAiPoints,
                'documentSupplements' => $package->documentSupplements,
            ];
        }

        return $list;
    }

    /**
     * 套餐详情
     * @param array $params
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(array $params): array
    {
        $package = ZhanhuiPackageModel::getInstance()
            ->where('guid', $params['guid'])
            ->where('status', ZhanhuiPackageModel::STATUS_ONLINE)
            ->where('deleted_at', 'null')
            ->findOrEmpty();
        
        if ($package->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '套餐不存在或已下架');
        }

        return [
            'guid' => $package->guid,
            'packageName' => $package->packageName,
            'packageDescription' => $package->packageDescription,
            'packagePrice' => fen_to_yuan($package->packagePrice),
            'validityPeriod' => $package->validityPeriod,
            'initialAiPoints' => $package->initialAiPoints,
            'documentSupplements' => $package->documentSupplements,
        ];
    }
}
