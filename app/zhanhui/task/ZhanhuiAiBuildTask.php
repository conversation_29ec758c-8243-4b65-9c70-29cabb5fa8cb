<?php

namespace app\zhan<PERSON>\task;

use app\libraries\service\ai_workflow\prompt\PromptBuildService;
use app\libraries\service\ai_workflow\tools\AzureAiTool;
use app\libraries\service\ai_workflow\tools\ZhipuTool;
use app\libraries\service\dify\XinghuoDatabaseService;
use app\zhanhui\cache\ZhanhuiCache;
use app\zhanhui\models\MerchantZhanhuiBannerModel;
use app\zhanhui\models\MerchantZhanhuiBuyRecordInfoModel;
use app\zhanhui\models\MerchantZhanhuiBuyRecordModel;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiFaqModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use Cls\Log;
use yunwuxin\cron\Task;

class ZhanhuiAiBuildTask extends Task
{
    public XinghuoDatabaseService $xinghuoDatabaseService;  // 星火知识库服务

    protected function configure()
    {
        return $this->everyFiveMinutes();
    }

    public function execute()
    {
        $xinghuoDatabaseService = new XinghuoDatabaseService();
        $this->xinghuoDatabaseService = $xinghuoDatabaseService;
        // 获取文件已经上传并且状态为未处理的展会数据
        $runList = MerchantZhanhuiBuyRecordInfoModel::getInstance()
            ->with(['record'])
            ->where('run_status', MerchantZhanhuiBuyRecordInfoModel::STATUS_RUN_WAIT)
            ->where('fill_progress', MerchantZhanhuiBuyRecordInfoModel::FILL_PROGRESS_FINISH)
            ->limit(10)
            ->select();
        foreach ($runList as $run) {
            $lockKey = 'zhanhui_ai_build_lock_' . $run->guid;
            $lock = ZhanhuiCache::getInstance()->lock($lockKey, 120);
            if (!$lock) {
                var_dump('获取锁失败');
                continue;
            }
            LogInfo('ZhanhuiAiBuildTask', '展会AI构建任务', '开始构建展会AI', [
                'record' => $run->toArray(),
                'time' => date('Y-m-d H:i:s', $run->time),
            ]);
            if (!$run->xinghuoFileId) {
                var_dump('文件开始上传到星火知识库');
                // 上传文件到星火知识库，用于后续的文档知识提取
                $result = $this->xinghuoDatabaseService->uploadFile($run->knowledgeFilePath);
                if (!$result || empty($result['fileId']) || $result['fileStatus'] == 'failed') {
                    $run->status = MerchantZhanhuiBuyRecordInfoModel::STATUS_RUN_FAIL;
                    $run->save();
                    continue;
                }
                $run->xinghuoFileId = $result['fileId'];
                $run->save();
                var_dump('完成知识库文件创建');
                // 等待20秒，等待文件向量化完成
                sleep(20);
            }
            $this->buildZhanhuiByAi($run);
        }
    }

    private function buildZhanhuiByAi($record)
    {
        var_dump('开始构建展会AI');
        // 获取文件向量化结果
        $result = $this->xinghuoDatabaseService->fileStatusQuery($record->xinghuoFileId);
        if (!$result || empty($result['fileStatus']) || $result['fileStatus'] != 'vectored') {
            var_dump($result);
            LogInfo('ZhanhuiAiBuildTask', '展会AI构建任务', '文件向量化未完成', [
                'record' => $record->toArray(),
                'result' => $result
            ]);
            return;
        }
        // 开始正式执行AI构建
        $record->runStatus = MerchantZhanhuiBuyRecordInfoModel::STATUS_RUN_ING;
        $record->save();
        /**
         * 执行工作流步骤：
         *  1. 根据星火文档问答生成关于展会的简介，生成的内容需要参考用户输入的要求
         *  2. 根据展会的简介，生成展会的logo图片、展会封面图片、slogan标语，其中logo图片的大小为固定的1024x1024，展会封面图片的大小为固定的1792x1024
         *  3. 完善展会的AI助理对话欢迎语、AI对话提示词
         *  4. 根据用户预设的三个模块，使用星火文档的分段内容，因此让智谱AI的json文档内容判断每一段的内容属于哪个模块
         *  5. 识别到这个分段属于某个模块后，使用智谱json文档生成这个模块可提取的常见问题和答案的json格式进行常见问题提取
         *  6. 当这个模块的图片不足四张时，系统判断这个场景问题是否可以设计一个封面图，如果可以，设计一个封面图，尺寸为模块一为1792x1024，模块二为1024x1024，模块三为1024x1024
         *  7. 如果这个内容不属于任意一个模块，AI判断是否能生成一个可用的常见问题和答案
         *  8. 从所有的问题列表中，选择用户可能关注的20个常见热门问题，生成首页的滚动问题列表，问题类型需要分为：推荐、通知、消息、热门
         *
         */
        // 完善展会的AI助理对话欢迎语、AI对话提示词
        $this->buildZhanhuiAiAssistant($record);
        //获取展会简介
        $intro = $this->getZhanhuiIntro($record);
        // 通过简介生成展会的logo图片、展会封面图片、slogan标语
        $this->buildZhanhuiImages($record, $intro);
        // 根据用户预设的三个模块，使用星火文档的分段内容，因此让智谱AI的json文档内容判断每一段的内容属于哪个模块
        // 识别到这个分段属于某个模块后，使用智谱json文档生成这个模块可提取的常见问题和答案的json格式进行常见问题提取
        $this->buildZhanhuiFaqs($record);
        //构建首页滚动问题、常用工具、聊天对话下的常见问题
        //选择分类为模块1的3个问题作为模块1轮播图问题
        //选择分类为模块2的6个问题作为模块2轮播图问题
        //分类为模块3的6个问题作为模块3轮播图问题
        $this->buildIndexFaq($record);
        // 构建完成
        $record->runStatus = MerchantZhanhuiBuyRecordInfoModel::STATUS_RUN_SUCCESS;
        $record->save();

        // 更新展会状态为正常
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $record['record']['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo) {
            $zhanhuiInfo->status = MerchantZhanhuiModel::STATUS_NORMAL;
            $zhanhuiInfo->save();
        }

        MerchantZhanhuiBuyRecordModel::getInstance()
            ->where('order_no', $record->buy_record_order_no)
            ->update([
                'order_status' => MerchantZhanhuiBuyRecordModel::ORDER_STATUS_SUCCESS
            ]);
    }

    /**
     * 获取展会简介
     * @param $record
     * @return string
     */
    private function getZhanhuiIntro($record)
    {
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $record['record']['zhanhuiGuid'])
            ->findOrEmpty();
        if (!$zhanhuiInfo) {
            LogInfo('ZhanhuiAiBuildTask', '展会AI构建任务', '获取展会信息失败', [
                'record' => $record->toArray(),
                'zhanhuiInfo' => $zhanhuiInfo
            ]);
            return '';
        }
        if ($zhanhuiInfo->description && $zhanhuiInfo->description != '等待AI生成中...') {
            return $zhanhuiInfo->description;
        }
        var_dump('开始获取展会简介');
        $themeDesc = $record->theme_desc ?? '展示关键信息';
        $prompt = PromptBuildService::getInstance()->zhanhuiIntro($themeDesc);
        try {
            $intro = $this->xinghuoDatabaseService->fileQA($record->xinghuoFileId, $prompt);
        } catch (\Exception $e) {
            LogInfo('ZhanhuiAiBuildTask', '展会AI构建任务', '获取展会简介失败', [
                'record' => $record->toArray(),
                'e' => $e->getMessage()
            ]);
            return '';
        }
        if (!$intro) {
            LogInfo('ZhanhuiAiBuildTask', '展会AI构建任务', '获取展会简介失败', [
                'record' => $record->toArray(),
                'intro' => $intro
            ]);
            return '';
        }
        // 更新当前展会的简介
        $zhanhuiGuid = $record['record']['zhanhuiGuid'];
        MerchantZhanhuiModel::getInstance()->where('guid', $zhanhuiGuid)->update(['description' => $intro]);

        var_dump('完成展会简介获取成功：' . $intro);

        return $intro;
    }

    /**
     * 通过简介生成展会的logo图片、展会封面图片、slogan标语
     * @param $record
     * @param $intro
     */
    private function buildZhanhuiImages($record, $intro)
    {
        var_dump('开始构建展会图片和展示信息');
        try {
            $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
                ->where('guid', $record['record']['zhanhuiGuid'])
                ->findOrEmpty();
            if (!$zhanhuiInfo) {
                LogInfo('buildZhanhuiImages', '展会AI构建任务', '获取展会信息失败', [
                    'record' => $record->toArray(),
                    'zhanhuiInfo' => $zhanhuiInfo
                ]);
                return;
            }
            if (!empty($zhanhuiInfo->logo) && !empty($zhanhuiInfo->cover) && !empty($zhanhuiInfo->slogo)) {
                return;
            }
            $themeDesc = $zhanhuiInfo->description;
            // 生成slogan标语
            $sloganPrompt = PromptBuildService::getInstance()->zhanhuiSlogan($intro, $themeDesc);
            $sloganResult = ZhipuTool::getInstance()->completeText($sloganPrompt);
            var_dump('生成的标语：' . $sloganResult);
            // 更新当前展会的标语
            $zhanhuiInfo->slogo = $sloganResult;
            $zhanhuiInfo->save();

            // 生成logo
            // 生成封logo文案
            $coverPrompt = PromptBuildService::getInstance()->logoDesign($themeDesc, $sloganResult);
            $coverPromptText = ZhipuTool::getInstance()->completeText($coverPrompt);
            // 生成logo
            $logoImg = AzureAiTool::getInstance()->dall3ImgBuild($coverPromptText);
            $zhanhuiInfo->logo = $logoImg;
            $zhanhuiInfo->save();
            var_dump('生成的logo：' . $logoImg);

            // 生成封面图
            $coverPrompt = PromptBuildService::getInstance()->zhanhuiCoverDesign($zhanhuiInfo->description);
            $coverPromptText = ZhipuTool::getInstance()->completeText($coverPrompt);
            $coverImg = AzureAiTool::getInstance()->dall3ImgBuild($coverPromptText, '1792x1024');
            $zhanhuiInfo->cover = $coverImg;
            $zhanhuiInfo->save();

            var_dump('生成的封面图：' . $coverImg);

            return;
        } catch (\Exception $e) {
            LogInfo('buildZhanhuiImages', '展会AI构建任务', '通过简介生成展会的logo图片、展会封面图片、slogan失败', [
                'record' => $record->toArray(),
                'e' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return;
        }
    }

    /**
     * AI助理对话欢迎语、AI对话提示词
     * @param $record
     * @return void
     */
    private function buildZhanhuiAiAssistant($record)
    {
        var_dump('开始构建展会AI助理对话欢迎语、AI对话提示词');
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $record['record']['zhanhuiGuid'])
            ->findOrEmpty();
        if (!$zhanhuiInfo) {
            LogInfo('buildZhanhuiAiAssistant', '展会AI构建任务', '获取展会信息失败', [
                'record' => $record->toArray(),
                'zhanhuiInfo' => $zhanhuiInfo
            ]);
            return;
        }
        $existConfig = MerchantZhanhuiConfigModel::getInstance()
            ->where('zhanhui_guid', $zhanhuiInfo->guid)
            ->findOrEmpty();
        if (!$existConfig->isEmpty()) {
            return;
        }
        $modelInfo = $record['modules'];
        // 创建展会配置
        $zhanhuiConfig = new MerchantZhanhuiConfigModel();
        $zhanhuiConfig->merchantGuid = $record['record']['merchantGuid'];
        $zhanhuiConfig->zhanhuiGuid = $zhanhuiInfo->guid;
        //  模块1自定义名称
        $zhanhuiConfig->topicBannerTitle = $modelInfo[0]['name'] ?? '推荐议程';
        // 模块2自定义名称
        $zhanhuiConfig->recommendCompanyTitle = $modelInfo[1]['name'] ?? '推荐企业';
        // 模块3自定义名称
        $zhanhuiConfig->guestBannerTitle = $modelInfo[2]['name'] ?? '推荐嘉宾';
        $zhanhuiConfig->welcomeText = '欢迎来到' . $zhanhuiInfo->name . '！我是您的智能助手。 请问有什么可以帮助您的吗？';
        $zhanhuiConfig->knowledgePrompt = '本次用户提问有匹配的知识库内容，请你参考知识库的内容进行回答，尽量使用我为你提供的知识库内容，知识库内容是：';
        $zhanhuiConfig->aiChatPrompt = '请你作为展会助手AI，为用户提供热情、专业、耐心的服务。行为准则：友好热情： 以积极的态度回应用户，使用礼貌和鼓励性的语言。专业准确： 提供的信息需要准确无误，确保用户获得可靠的展会信息。耐心细致： 对于用户的问题，即使重复或复杂，也要耐心解答，不急躁。主动提供帮助： 在用户提问时，除了回答问题，还可以主动提供额外相关信息或建议。';
        $zhanhuiConfig->save();

        var_dump('完成展会AI助理对话欢迎语、AI对话提示词');
        return;
    }

    /**
     * 构建首页滚动问题、常用工具、聊天对话下的常见问题
     * @param $record
     * @return void
     */
    public function buildZhanhuiFaqs($record)
    {
        var_dump('开始构建展会常见问题');
        LogInfo('buildZhanhuiModules', '展会AI构建任务', '开始构建展会模块', [
            'record' => $record['record']['zhanhuiGuid'],
        ]);
        try {
            // 模块1中的默认封面图
            $defaultCover = [
                'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/6a8ff8d445cb492fbefed3f97e0cb4f3.png',
                'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/3b9d038d2ac4469b93424c02a85d3742.png',
                'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/76c1420be44e4a8683620d24dc4fcca2.png'
            ];
            // 模块2、3的默认封面图
            $defaultCover2 = 'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/018b150550d84186a346591e4a8f18ec.png';
            // 获取星火文档切分的文档片段
            $fileChunkLists = $this->xinghuoDatabaseService->fileChunkLists($record['xinghuoFileId']);
            // 根据当前文档片段提取常见问题
            // 为减少AI的调用次数，1次取3段内容中的content作为一次请求
            $mergeChunks = [];
            $chunkCount = count($fileChunkLists);
            $chunkIndex = 0;
            $chunkSize = 3;
            while ($chunkIndex < $chunkCount) {
                $chunks = array_slice($fileChunkLists, $chunkIndex, $chunkSize);
                $chunkIndex += $chunkSize;
                $content = '';
                foreach ($chunks as $chunk) {
                    $content .= $chunk['content'];
                }
                $mergeChunks[] = $content;
            }
            $zhanhuiFaqs = [];
            // 问题所属模块分类
            $faqCates = [$record['modules'][0]['name'], $record['modules'][1]['name'], $record['modules'][2]['name'], '其他'];
            $faqCates = join(',', $faqCates);
            // 模块1轮播图列表（3张）
            $module_1_banners = [];
            // 模块2轮播图列表（6张）
            $module_2_banners = [];
            // 模块3轮播图列表（6张）
            $module_3_banners = [];
            // 问题提取的最大次数
            $maxCount = 0;
            foreach ($mergeChunks as $index => $chunk) {
                if ($maxCount >= 50) {
                    break;
                }
                $prompt = PromptBuildService::getInstance()->zhanhuiFaqs($chunk, $faqCates);
                $result = AzureAiTool::getInstance()->gpt4oChatComplete($prompt, 'json');
                $maxCount++;
                // 休息0.2秒
                usleep(200000);
                $faqs = json_decode($result, true);
                logInfo('buildZhanhuiModules', '展会AI构建任务', '常见问题提取', [
                    'record' => $record['record']['zhanhuiGuid'],
                    'faqs' => $faqs
                ]);
                $faqPositionByCate = [
                    $record['modules'][0]['name'] => 2,
                    $record['modules'][1]['name'] => 2,
                    $record['modules'][2]['name'] => 2,
                    '其他' => 1
                ];
                // 工具图标随机获取
                $iconType = ['notice', 'msg', 'hot', 'recommend', 'tool'];
                if (!empty($faqs['questions']) && count($faqs['questions']) > 0) {
                    $addZhanhuiFaqData = [];
                    foreach ($faqs['questions'] as $faq) {
                        $faqCate = $faq['cate'] ?? '其他';
                        //  根据问题分类获取问题所属模块
                        $addZhanhuiFaqData[] = [
                            'guid' => get_guid(),
                            'merchant_guid' => $record['record']['merchantGuid'],
                            'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                            'question' => $faq['question'],
                            'ai_chat_question' => $faq['question'],
                            'answer_type' => MerchantZhanhuiFaqModel::ANSWER_TYPE_RICH_TEXT,
                            'answer' => $faq['answer'],
                            'position' => $faqPositionByCate[$faqCate] ?? 1,
                            'icon_type' => $iconType[array_rand($iconType)],
                            'create_time' => time(),
                        ];
                        // 判断当前问题属于3个模块中的哪一个，如果回答内容的长度大于30，则这个问题进入该模块
                        if (mb_strlen($faq['answer']) > 30) {
                            if ($faqCate == $record['modules'][0]['name']) {
                                $module_1_banners[] = $faq;
                            } elseif ($faqCate == $record['modules'][1]['name']) {
                                $module_2_banners[] = $faq;
                            } elseif ($faqCate == $record['modules'][2]['name']) {
                                $module_3_banners[] = $faq;
                            }
                        }
                    }
                    $faqModel = new MerchantZhanhuiFaqModel();
                    $faqModel->saveAll($addZhanhuiFaqData);
                }
                var_dump('完成了一次问题提取');
            }
            var_dump('轮播图1的条数：' . count($module_1_banners));

            // 创建模块1中的轮播图问题（只取前3个）
            $module_1_banners = array_slice($module_1_banners, 0, 3);
            $zhanhuiBannerData1 = [];
            foreach ($module_1_banners as $key => $faq) {
                // 获取这个问题的Guid
                $faqGuid = $faqModel
                    ->where('zhanhui_guid', $record['record']['zhanhuiGuid'])
                    ->where('question', $faq['question'])
                    ->value('guid');
                $zhanhuiBannerData1[] = [
                    'guid' => get_guid(),
                    'merchant_guid' => $record['record']['merchantGuid'],
                    'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                    'banner_type' => 1,
                    'title' => $faq['question'],
                    'content' => $faq['answer'],
                    'image' => $defaultCover[$key] ?? ($defaultCover[0] ?? ''),
                    'jump_type' => 2,
                    'jump_value' => $faqGuid,
                ];
            }
            $bannerModel = new MerchantZhanhuiBannerModel();
            if (!empty($zhanhuiBannerData1)) {
                var_dump('完成了一次问题与轮播图1构建');
                $bannerModel->saveAll($zhanhuiBannerData1);
            }

            // 创建模块2中的轮播图问题（只取前6个）
            $module_2_banners = array_slice($module_2_banners, 0, 6);
            $zhanhuiBannerData2 = [];
            foreach ($module_2_banners as $key => $faq) {
                // 获取这个问题的Guid
                $faqGuid = $faqModel
                    ->where('zhanhui_guid', $record['record']['zhanhuiGuid'])
                    ->where('question', $faq['question'])
                    ->value('guid');
                $zhanhuiBannerData2[] = [
                    'guid' => get_guid(),
                    'merchant_guid' => $record['record']['merchantGuid'],
                    'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                    'banner_type' => 3,
                    'title' => $faq['question'],
                    'content' => $faq['answer'],
                    'image' => $defaultCover2,
                    'jump_type' => 2,
                    'jump_value' => $faqGuid,
                ];
            }
            if (!empty($zhanhuiBannerData2)) {
                var_dump('完成了一次问题与轮播图2构建');
                $bannerModel->saveAll($zhanhuiBannerData2);
            }

            // 创建模块3中的轮播图问题（只取前10个）
            $module_3_banners = array_slice($module_3_banners, 0, 10);
            $zhanhuiBannerData3 = [];
            foreach ($module_3_banners as $key => $faq) {
                // 获取这个问题的Guid
                $faqGuid = $faqModel
                    ->where('zhanhui_guid', $record['record']['zhanhuiGuid'])
                    ->where('question', $faq['question'])
                    ->value('guid');
                $zhanhuiBannerData3[] = [
                    'guid' => get_guid(),
                    'merchant_guid' => $record['record']['merchantGuid'],
                    'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                    'banner_type' => 2,
                    'title' => $faq['question'],
                    'content' => $faq['answer'],
                    'image' => $defaultCover2,
                    'jump_type' => 2,
                    'jump_value' => $faqGuid,
                ];
            }
            if (!empty($zhanhuiBannerData3)) {
                var_dump('完成了一次问题与轮播图3构建');
                $bannerModel->saveAll($zhanhuiBannerData3);
            }
        } catch (\Exception $e) {
            LogInfo('buildZhanhuiModules', '展会AI构建任务', '构建首页滚动问题、常用工具、聊天对话下的常见问题', [
                'record' => $record['record']['zhanhuiGuid'],
                'e' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * 选择用户可能关注的20个常见热门问题，生成首页的滚动问题列表
     * @param $record
     * @return void
     */
    private function buildIndexFaq($record)
    {
        var_dump('开始构建首页滚动问题');
        //取60个问题中精选20个问题作为首页滚动问题
        $allFaqs = MerchantZhanhuiFaqModel::getInstance()
            ->where('zhanhui_guid', $record['record']['zhanhuiGuid'])
            ->field('sys_id,question')
            ->limit(80)
            ->select()
            ->toArray();
        // 从80个问题中让AI选择30个问题作为首页滚动问题
        $aiReadFaqs = json_encode(['questions' => $allFaqs], JSON_UNESCAPED_UNICODE);
        $indexFaqPrompt = PromptBuildService::getInstance()->zhanhuiIndexFaqs($aiReadFaqs);
        logInfo('buildZhanhuiModules', '展会AI构建任务', '首页滚动问题', [
            'record' => $record['record']['zhanhuiGuid'],
            'indexFaqPrompt' => $indexFaqPrompt
        ]);
        $indexFaqResult = AzureAiTool::getInstance()->gpt4oChatComplete($indexFaqPrompt, 'json');
        $faqIndexList = json_decode($indexFaqResult, true);
        logInfo('buildZhanhuiModules', '展会AI构建任务', '首页滚动问题结果', [
            'record' => $record['record']['zhanhuiGuid'],
            'faqIndexList' => $faqIndexList
        ]);
        var_dump('完成首页滚动问题构建');
        // 复制这些问题到首页滚动问题
        $addIndexFaqData = [];
        $indexFaqSysIds = array_column($faqIndexList['questions'], 'sysId');
        $allIndexFaq = array_filter($allFaqs, function ($faq) use ($indexFaqSysIds) {
            return in_array($faq['sysId'], $indexFaqSysIds);
        });
        var_dump($allIndexFaq);
        $allFaqBySysId = array_column($allIndexFaq, 'question', 'sysId');
        // 工具类的单独提取前二个作为常用工具
        $toolFaq = [];
        foreach ($faqIndexList['questions'] as $faq) {
            if (!isset($allFaqBySysId[$faq['sysId']])) {
                var_dump('问题不存在');
                continue;
            }
            $answer = MerchantZhanhuiFaqModel::getInstance()
                ->where('sys_id', $faq['sysId'])
                ->value('answer');
            if (!$answer) {
                var_dump('问题答案不存在');
                continue;
            }
            // 首页滚动的图标只能是msg、notice、hot、recommend
            $indexFaqIcon = 'msg';
            if ($faq['icon'] != 'tool') {
                $indexFaqIcon = $faq['icon'];
            }
            $addIndexFaqData[] = [
                'guid' => get_guid(),
                'merchant_guid' => $record['record']['merchantGuid'],
                'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                'question' => $allFaqBySysId[$faq['sysId']],
                'ai_chat_question' => $allFaqBySysId[$faq['sysId']],
                'answer_type' => MerchantZhanhuiFaqModel::ANSWER_TYPE_RICH_TEXT,
                'answer' => $answer,
                'position' => 1,
                'icon_type' => $indexFaqIcon,
                'create_time' => time(),
            ];
            if (count($toolFaq) < 3 && $faq['icon'] == 'tool') {
                var_dump('获取了一个工具问题');
                var_dump($faq);
                $toolFaq[] = [
                    'guid' => get_guid(),
                    'merchant_guid' => $record['record']['merchantGuid'],
                    'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                    'question' => $allFaqBySysId[$faq['sysId']],
                    'ai_chat_question' => $allFaqBySysId[$faq['sysId']],
                    'answer_type' => MerchantZhanhuiFaqModel::ANSWER_TYPE_RICH_TEXT,
                    'answer' => $answer,
                    'position' => 4,
                    'icon_type' => 'msg',
                    'create_time' => time(),
                ];
            }
        }
        $faqModel = new MerchantZhanhuiFaqModel();
        $faqModel->saveAll($addIndexFaqData);
        // 新增常用工具问题
        $faqModel = new MerchantZhanhuiFaqModel();
        $faqModel->saveAll($toolFaq);
        // 取精选问题的前6个作为欢迎语下方常见问题
        $welcomeFaq = array_slice($addIndexFaqData, 0, 6);
        $welcomeFaqData = [];
        foreach ($welcomeFaq as $faq) {
            $welcomeFaqData[] = [
                'guid' => get_guid(),
                'merchant_guid' => $record['record']['merchantGuid'],
                'zhanhui_guid' => $record['record']['zhanhuiGuid'],
                'question' => $faq['question'],
                'ai_chat_question' => $faq['question'],
                'answer_type' => MerchantZhanhuiFaqModel::ANSWER_TYPE_RICH_TEXT,
                'answer' => $faq['answer'],
                'position' => 3,
                'icon_type' => $faq['icon_type'],
                'create_time' => time(),
            ];
            var_dump('新增了一个欢迎问题');
        }
        $faqModel = new MerchantZhanhuiFaqModel();
        $faqModel->saveAll($welcomeFaqData);

        var_dump('完成首页滚动问题构建');

        return;
    }
}
