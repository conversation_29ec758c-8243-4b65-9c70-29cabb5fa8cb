<?php

/**
 * @author: xuz<PERSON><PERSON><PERSON>
 * @Time: 2024/12/17   22:28
 */

namespace app\zhanhui\request\api;

use app\Request;

class OrderRequest extends Request
{
    protected function getRule(): array
    {
        return [
            'createOrder' => [
                'zhanhuiName' => 'require',
            ],
            'orderInfo' => [
                'zhanhuiGuid' => 'require',
            ],
            'createOrderByCode' => [
                'zhanhuiName' => 'require',
                'exchangeCode' => 'require',
            ],
            'createOrderByPackage' => [
                'zhanhuiName' => 'require',
                'packageGuid' => 'require',
            ],
        ];
    }

    protected array $msgs = [
        'guid' => '展会标识不能为空',
        'zhanhuiGuid' => '展会标识不能为空',
        'zhanhuiName' => '展会名称不能为空',
        'position' => 'FAQ位置不能为空',
        'exchangeCode' => '兑换码不能为空',
        'packageGuid' => '套餐标识不能为空',
    ];
}
