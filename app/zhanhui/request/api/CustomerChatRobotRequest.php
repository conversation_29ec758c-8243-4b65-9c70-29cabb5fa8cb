<?php

namespace app\zhanhui\request\api;

use app\Request;

class CustomerChatRobotRequest extends Request
{
    protected array $msgs = [
        'merchantGuid.require' => '商户GUID不能为空',
        'merchantGuid.length' => '商户GUID长度必须为32个字符',
        'robotType.require' => '机器人类型不能为空',
        'robotType.in' => '机器人类型必须是抖音、小红书或微信',
        'robotName.require' => '机器人名称不能为空',
        'robotName.max' => '机器人名称长度不能超过3000个字符',
        'robotAvatar.require' => '机器人头像不能为空',
        'robotAvatar.url' => '机器人头像必须是有效的URL',
        'robotAvatar.max' => '机器人头像URL长度不能超过255个字符',
        'robotDesc.max' => '机器人描述长度不能超过255个字符',
        'otherPlatformUserUuid.require' => '第三方平台用户UUID不能为空',
        'otherPlatformUserUuid.max' => '第三方平台用户UUID长度不能超过255个字符',
        'otherPlatformUserNickname.require' => '第三方平台用户昵称不能为空',
        'otherPlatformUserNickname.max' => '第三方平台用户昵称长度不能超过255个字符',
        'otherPlatformUserAvatar.require' => '第三方平台用户头像不能为空',
        'otherPlatformUserAvatar.url' => '第三方平台用户头像必须是有效的URL',
        'otherPlatformUserAvatar.max' => '第三方平台用户头像URL长度不能超过255个字符',
        'clientRobotName.max' => '客户端机器人名称长度不能超过255个字符',
        'extendConfig.json' => '扩展配置必须是有效的JSON格式',
        'deviceId.require' => '设备ID不能为空',
        'deviceId.max' => '设备ID长度不能超过255个字符',
        'otherPlatformChatUuid.require' => '第三方平台聊天轮次UUID不能为空',
        'otherPlatformChatUuid.max' => '第三方平台聊天轮次UUID长度不能超过255个字符',
        'chatTitle.max' => '聊天标题长度不能超过255个字符',
        'chatContent.require' => '聊天内容不能为空',
        'chatContent.json' => '聊天内容必须是有效的JSON格式',
        'robotGuid.require' => '机器人 GUID 不能为空',
        'zhanhuiGuid.require' => '展会 GUID 不能为空',
        'zhanhuiGuids.require' => '展会 GUID 列表不能为空',
        'zhanhuiGuids.array' => '展会 GUID 列表必须是数组格式',
        'zhanhuiGuids.max' => '最多只能绑定3个展会',
        'roleGuid.require' => '角色 GUID 不能为空',
        'roleName.require' => '角色名称不能为空',
        'roleName.max' => '角色名称长度不能超过50个字符',
        'roleDesc.require' => '角色描述不能为空',
        'roleDesc.max' => '角色描述长度不能超过255个字符',
        'rolePrompt.require' => '角色提示词不能为空',
        'knowledgeBindType.require' => '知识库绑定类型不能为空',
        'knowledgeBindType.in' => '知识库绑定类型必须是默认或自定义',
        'customKnowledgeGuids.array' => '自定义知识库列表必须是数组格式',
        'taskGuid.require' => '任务 GUID 不能为空',
        'taskType.require' => '任务类型不能为空',
        'taskType.in' => '任务类型不正确',
        'taskContent.require' => '任务内容不能为空',
        'taskParams.array' => '任务参数必须是数组格式',
        'contentType.require' => '内容类型不能为空',
        'contentType.in' => '内容类型必须是文本、图片、视频、文件或工作流ID',
        'content.require' => '内容不能为空',
        'messageGuid.require' => '消息GUID不能为空',
    ];

    protected function getRule(): array
    {
        return [
            'register' => [
                'merchantGuid' => ['require', 'length:32'],
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],
                'robotName' => ['max:255'],
                'robotAvatar' => ['max:255'],
                'robotDesc' => ['max:255'],
                'otherPlatformUserUuid' => ['require', 'max:255'],
                'otherPlatformUserNickname' => ['max:255'],
                'otherPlatformUserAvatar' => ['url', 'max:3000'],
                'clientRobotName' => ['max:255'],
                'extendConfig' => ['json'],
                'deviceId' => ['require', 'max:255'],
            ],
            'updateChatRecord' => [
                'merchantGuid' => ['require', 'length:32'],
                'deviceId' => ['require', 'max:255'],
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],
                'otherPlatformUserUuid' => ['require', 'max:255'],
                'otherPlatformChatUuid' => ['require', 'max:255'],
                'chatTitle' => ['max:255'],
                'chatContent' => ['require'],
            ],
            'replyChat' => [
                'merchantGuid' => ['require', 'length:32'],
                'deviceId' => ['require', 'max:255'],
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],
                'otherPlatformUserUuid' => ['require', 'max:255'],
                'otherPlatformChatUuid' => ['require', 'max:255'],
                'chatTitle' => ['max:255'],
                'chatContent' => ['require'],
                'roleGuid' => ['max:32'],  // 可选参数，指定使用的角色GUID
            ],
            'assignZhanhui' => [
                'robotGuid' => ['require'],   // 要操作的机器人 GUID
                'zhanhuiGuids' => ['require', 'array', 'max:3'], // 要绑定的展会 GUID 列表，最多3个
            ],
            'roleList' => [
                'robotGuid' => ['require'],   // 要查询的机器人 GUID
            ],
            'roleDetail' => [
                'roleGuid' => ['require'],   // 要查询的角色 GUID
            ],
            'createRole' => [
                'robotGuid' => ['require'],   // 机器人 GUID
                'roleName' => ['require', 'max:50'],  // 角色名称
                'roleDesc' => ['require', 'max:255'],  // 角色描述
                'rolePrompt' => ['require'],  // 角色提示词
                'knowledgeBindType' => ['require', 'in:default,custom'],  // 知识库绑定类型
                'customKnowledgeGuids' => ['array'],  // 自定义知识库列表
            ],
            'updateRole' => [
                'roleGuid' => ['require'],   // 角色 GUID
                'roleName' => ['require', 'max:50'],  // 角色名称
                'roleDesc' => ['require', 'max:255'],  // 角色描述
                'rolePrompt' => ['require'],  // 角色提示词
                'knowledgeBindType' => ['require', 'in:default,custom'],  // 知识库绑定类型
                'customKnowledgeGuids' => ['array'],  // 自定义知识库列表
            ],
            'deleteRole' => [
                'roleGuid' => ['require'],   // 要删除的角色 GUID
            ],
            'submitTask' => [
                'merchantGuid' => ['require', 'length:32'],  // 商户 GUID
                'deviceId' => ['require', 'max:255'],  // 设备 ID
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],  // 机器人类型
                'otherPlatformUserUuid' => ['require', 'max:255'],  // 第三方平台用户 UUID
                'taskType' => ['require', 'in:chat_reply'],  // 任务类型
                'taskContent' => ['require'],  // 任务内容
                'taskParams' => ['array'],  // 任务参数
            ],
            'getTaskDetail' => [
                'taskGuid' => ['require'],   // 任务 GUID
            ],
            'getTaskList' => [
                'merchantGuid' => ['require', 'length:32'],  // 商户 GUID
                'deviceId' => ['require', 'max:255'],  // 设备 ID
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],  // 机器人类型
                'otherPlatformUserUuid' => ['require', 'max:255'],  // 第三方平台用户 UUID
                'taskType' => ['in:chat_reply'],  // 可选参数，任务类型
                'taskStatus' => ['in:1,2,3,4'],  // 可选参数，任务状态
                'page' => ['integer'],  // 页码
                'pageSize' => ['integer'],  // 每页数量
            ],
            // 新增三个接口的验证规则
            'addSendMessage' => [
                'robotGuid' => ['require'],  // 机器人 GUID
                'otherPlatformChatUuid' => ['require'],
                'contentType' => ['require', 'in:text,image,video,file,job'],  // 内容类型
                'content' => ['require'],  // 内容
            ],
            'pollSendMessage' => [
                'merchantGuid' => ['require', 'length:32'],  // 商户 GUID
                'deviceId' => ['require', 'max:255'],  // 设备 ID
                'robotType' => ['require', 'in:douyin,xiaohongshu,weixin'],  // 机器人类型
                'otherPlatformUserUuid' => ['require', 'max:255'],  // 第三方平台用户 UUID
                'otherPlatformChatUuid' => ['require', 'max:255'],  // 第三方平台聊天轮次UUID
            ],
            'deleteSendMessage' => [
                'messageGuid' => ['require'],  // 消息 GUID
            ],
        ];
    }
}