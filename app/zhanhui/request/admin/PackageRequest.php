<?php

namespace app\zhanhui\request\admin;

use app\Request;

class PackageRequest extends Request
{
    protected function getRule(): array
    {
        return [
            'create' => [
                'packageName' => 'require',
                'packageDescription' => 'require',
                'packagePrice' => 'require|number|egt:0',
                'validityPeriod' => 'require|number|gt:0',
                'initialAiPoints' => 'require|number|egt:0',
                'documentSupplements' => 'require|number|egt:0',
                'status' => 'require|in:0,1'
            ],
            'update' => [
                'guid' => 'require',
                'packageName' => 'require',
                'packageDescription' => 'require',
                'packagePrice' => 'require|number|egt:0',
                'validityPeriod' => 'require|number|gt:0',
                'initialAiPoints' => 'require|number|egt:0',
                'documentSupplements' => 'require|number|egt:0',
                'status' => 'require|in:0,1'
            ],
            'delete' => [
                'guid' => 'require'
            ],
            'detail' => [
                'guid' => 'require'
            ],
            'changeStatus' => [
                'guid' => 'require',
                'status' => 'require|in:0,1'
            ]
        ];
    }

    protected array $msgs = [
        'packageName' => '套餐名称不能为空',
        'packageDescription' => '套餐描述不能为空',
        'packagePrice' => '套餐价格必须为非负数',
        'validityPeriod' => '套餐有效期必须大于0',
        'initialAiPoints' => '初始AI点数必须为非负数',
        'documentSupplements' => '文档补充次数必须为非负数',
        'status' => '状态值无效',
        'guid' => '套餐标识不能为空'
    ];
}
