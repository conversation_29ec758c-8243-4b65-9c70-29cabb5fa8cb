<?php

namespace app\zhanhui\controller\api;

use app\user\ApiBaseController;
use app\zhanhui\logic\api\PackageLogic;
use app\zhanhui\request\api\PackageRequest;

class Package extends ApiBaseController
{
    /**
     * 套餐列表
     * @param PackageLogic $logic
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function list(PackageLogic $logic): array
    {
        return $logic->list();
    }

    /**
     * 套餐详情
     * @param PackageRequest $request
     * @param PackageLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function detail(PackageRequest $request, PackageLogic $logic): array
    {
        return $logic->detail($request->param());
    }
}
