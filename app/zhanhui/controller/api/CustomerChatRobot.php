<?php

namespace app\zhanhui\controller\api;

use app\constdir\SysErrorCode;
use app\libraries\service\token\TokenService;
use app\user\ApiBaseController;
use app\zhanhui\logic\api\CustomerChatRobotLogic;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\zhanhui\request\api\CustomerChatRobotRequest;
use app\libraries\exception\ApiException;

/**
 *  当前控制器存在部分外部设备调用接口，外部设备调用接口需要验证请求头中的robot key，不走用户登录验证
 */
class CustomerChatRobot extends ApiBaseController
{
    /**
     * 不需要登录验证的方法
     * @var array
     */
    protected $middleware = [
        'token' => [
            'except' => ['register', 'updateChatRecord', 'replyChat', 'submitTask', 'getTaskDetail', 'getTaskList', 'pollSendMessage', 'deleteSendMessage'],
        ],
        'must_login' => [
            'except' => ['register', 'updateChatRecord', 'replyChat', 'submitTask', 'getTaskDetail', 'getTaskList', 'pollSendMessage', 'deleteSendMessage'],
        ],
        'signature' => [
            'except' => ['register', 'updateChatRecord', 'replyChat', 'submitTask', 'getTaskDetail', 'getTaskList', 'pollSendMessage', 'deleteSendMessage'],
        ],
    ];


    // ------------------------ 外部设备调用相关  -------------------------------------------

    /**
     * 验证机器人请求头（外部设备调用）
     * @throws ApiException
     */
    protected function checkRobotKey()
    {
        $robotKey = request()->header('X-Robot-Key');
        if (empty($robotKey)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, 'robot key不能为空');
        }

        $configKey = env('robot.key', '');
        if (empty($configKey) || $robotKey !== $configKey) {
            throwException(SysErrorCode::SYS_ERROR_CODE, 'robot key不正确');
        }
    }

    /**
     * 注册客服机器人
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     */
    public function register(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->register($request->param());
    }

    /**
     * 更新客服机器人聊天记录
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     */
    public function updateChatRecord(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->updateChatRecord($request->param());
    }

    /**
     * 回复客服机器人聊天
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     */
    public function replyChat(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->replyChat($request->param());
    }

    /**
     * 提交客服机器人任务
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function submitTask(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->submitTask($request->param());
    }

    /**
     * 获取客服机器人任务详情
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function getTaskDetail(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->getTaskDetail($request->param());
    }

    /**
     * 获取客服机器人任务列表
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function getTaskList(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->getTaskList($request->param());
    }

    /**
     * 轮询某个聊天对话轮次有没有待发送的消息
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function pollSendMessage(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->pollSendMessage($request->param());
    }

    /**
     * 删除待发送消息
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function deleteSendMessage(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkRobotKey();
        return $logic->deleteSendMessage($request->param());
    }


    // ------------------------ 外部设备调用结束  -------------------------------------------


    // ------------------------ 下方为内部调用接口，走用户登录认证  -------------------------------------------

    /**
     * 判断是否是展会管理员(内部调用)
     * @param $zhanhuiGuid
     * @throws ApiException
     */
    private function checkIsSuperAdmin()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('is_super_admin', MerchantZhanhuiAdminUserModel::IS_SUPER_ADMIN)
            ->findOrEmpty();
        if ($existAdmin->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您不是展会管理员，无权操作');
        }
        if ($existAdmin['isSuperAdmin'] != MerchantZhanhuiAdminUserModel::IS_SUPER_ADMIN) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您不是超级管理员，无权操作');
        }
    }


    /**
     * 客服机器人列表
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return mixed
     */
    public function lists(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        $this->checkIsSuperAdmin();
        return $logic->lists();
    }

    /**
     * 客服机器人展会批量分配
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function assignZhanhui(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        // 验证是否为超级管理员
        $this->checkIsSuperAdmin();
        return $logic->assignZhanhui($request->param());
    }

    /**
     * 获取机器人角色列表
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function roleList(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->roleList($request->param());
    }

    /**
     * 获取机器人角色详情
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function roleDetail(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->roleDetail($request->param());
    }

    /**
     * 创建机器人角色
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function createRole(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->createRole($request->param());
    }

    /**
     * 更新机器人角色
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function updateRole(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->updateRole($request->param());
    }

    /**
     * 删除机器人角色
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function deleteRole(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->deleteRole($request->param());
    }

    /**
     * 添加指定聊天轮次的待发送记录（内部调用接口）
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function addSendMessage(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->addSendMessage($request->param());
    }

    /**
     * 对话用户列表（内部调用接口，走用户登录认证）
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function chatUserList(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->chatUserList($request->param());
    }

    /**
     * 用户对话详情（内部调用接口，走用户登录认证）
     * @param CustomerChatRobotRequest $request
     * @param CustomerChatRobotLogic $logic
     * @return array
     * @throws ApiException
     */
    public function chatDetail(CustomerChatRobotRequest $request, CustomerChatRobotLogic $logic)
    {
        return $logic->chatDetail($request->param());
    }
}
