<?php

/**
 * @author: x<PERSON><PERSON><PERSON><PERSON>
 * @Time: 2024/12/17   22:27
 */

namespace app\zhanhui\controller\api;

use app\user\ApiBaseController;
use app\zhanhui\logic\api\OrderLogic;
use app\zhanhui\request\api\OrderRequest;

class Order extends ApiBaseController
{
    /**
     * 创建订单
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     */
    public function createOrder(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->createOrder($request->param());
    }

    /**
     * 通过兑换码创建订单
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function createOrderByCode(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->createOrderByCode($request->param());
    }

    /**
     * 查询展会订单付款情况
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     */
    public function queryOrder(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->queryOrder($request->param());
    }

    /**
     *  查询展会订单数据准备情况
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function orderInfo(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->orderInfo($request->param());
    }

    /**
     * 更新展会订单数据准备情况
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     */
    public function orderInfoUpdate(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->orderInfoUpdate($request->param());
    }

    /**
     * 购买的展会列表
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     */
    public function buyLists(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->buyLists($request->param());
    }

    /**
     * 根据套餐创建订单
     * @param OrderRequest $request
     * @param OrderLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createOrderByPackage(OrderRequest $request, OrderLogic $logic)
    {
        return $logic->createOrderByPackage($request->param());
    }
}
