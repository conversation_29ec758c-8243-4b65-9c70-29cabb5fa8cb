# 展位套餐管理系统相关数据库变更

# 创建套餐表
CREATE TABLE zhanhui_package
(
    `sys_id`                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`                       char(32)     NOT NULL COMMENT '唯一关键字段',
    `package_name`               varchar(255) NOT NULL DEFAULT '' COMMENT '套餐名称',
    `package_description`        text         NOT NULL COMMENT '套餐描述',
    `package_price`              int unsigned NOT NULL DEFAULT 0 COMMENT '套餐价格，单位分',
    `validity_period`            int unsigned NOT NULL DEFAULT 0 COMMENT '套餐有效期，天数',
    `initial_ai_points`          int unsigned NOT NULL DEFAULT 0 COMMENT '套餐初始AI点数',
    `document_supplements`       int unsigned NOT NULL DEFAULT 0 COMMENT '套餐文档补充次数',
    `status`                     tinyint unsigned NOT NULL DEFAULT 1 COMMENT '上架状态：1-上架，0-下架',
    `create_time`                int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`                int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`                 int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY                          guid (guid)
) COMMENT '展位套餐表';

# 展位表新增文档补充次数字段
ALTER TABLE `merchant_zhanhui` ADD COLUMN `document_supplements` int unsigned NOT NULL DEFAULT 0 COMMENT '文档补充次数';

# 订单表新增套餐相关字段
ALTER TABLE `merchant_zhanhui_buy_record` ADD COLUMN `package_guid` char(32) NOT NULL DEFAULT '' COMMENT '套餐GUID';
ALTER TABLE `merchant_zhanhui_buy_record` ADD COLUMN `package_info` json COMMENT '套餐信息JSON';
