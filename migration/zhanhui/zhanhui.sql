CREATE TABLE merchant_zhanhui
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `name`          varchar(255) NOT NULL DEFAULT '' COMMENT '展会名称',
    `short_name`    varchar(255) NOT NULL DEFAULT '' COMMENT '展会简称',
    `logo`          varchar(255) NOT NULL DEFAULT '' COMMENT '展会logo',
    `slogo`         varchar(255) NOT NULL DEFAULT '' COMMENT '展会标语',
    `cover`         varchar(255) NOT NULL DEFAULT '' COMMENT '展会封面',
    `description`   text         NOT NULL COMMENT '展会描述',
    `start_time`    int unsigned NOT NULL DEFAULT 0 COMMENT '展会开始时间',
    `end_time`      int unsigned NOT NULL DEFAULT 0 COMMENT '展会结束时间',
    `end_is_show`   tinyint unsigned NOT NULL DEFAULT 0 COMMENT '结束是否展示',
    `status`        tinyint unsigned NOT NULL DEFAULT 1 COMMENT '展会状态：1-开启；2-禁用',
    `ai_point`      int unsigned NOT NULL DEFAULT 0 COMMENT 'AI点数',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY             guid (guid)
)COMMENT '商家展会表';

CREATE TABLE merchant_zhanhui_config
(
    `sys_id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`               char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid`      char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`       char(32)     NOT NULL COMMENT '展会guid',
    `topic_banner_title` varchar(255) NOT NULL DEFAULT '推荐议程' COMMENT '议题banner标题',
    `guest_banner_title` varchar(255) NOT NULL DEFAULT '推荐嘉宾' COMMENT '嘉宾banner标题',
    `welcome_text`       text         NOT NULL DEFAULT '' COMMENT '对话欢迎文案',
    `is_require_phone`   tinyint unsigned NOT NULL DEFAULT 2 COMMENT '是否要求登记手机号码:1-是，2-否',
    `merchant_knowledge_guid` char(32) NOT NULL DEFAULT '' COMMENT '商家知识库guid',
    `knowledge_prompt`   text COMMENT '知识库使用提示词',
    `ai_chat_prompt`     text COMMENT 'AI对话提示词',
    `ai_chat_model`      varchar(255) NOT NULL DEFAULT '' COMMENT 'AI对话模型',
    `create_time`        int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`        int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`         int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY                  zhanhui_guid (zhanhui_guid)
)COMMENT '商家展会配置表';

CREATE TABLE merchant_zhanhui_faq
(
    `sys_id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`             char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid`    char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`     char(32)     NOT NULL COMMENT '展会guid',
    `question`         varchar(255) NOT NULL DEFAULT '' COMMENT '问题',
    `ai_chat_question` varchar(255) NOT NULL DEFAULT '' COMMENT '聊天对话页展示的问题',
    `answer_type`      tinyint unsigned NOT NULL DEFAULT 0 COMMENT '答案类型：1-富文本，2-图片，3-链接，4-视频',
    `answer`           text         NOT NULL COMMENT '答案',
    `position`         tinyint unsigned NOT NULL DEFAULT 0 COMMENT '展示位置：1-首页滚动，2-轮播图位置，3-欢迎语下方常见问题，4-底部常用工具',
    `icon_type`        varchar(255) NOT NULL DEFAULT '' COMMENT '问题图标类型: notice-通知，msg-消息，hot-热点，recommend-推荐，tool-工具',
    `sort`             int unsigned NOT NULL DEFAULT 0 COMMENT '排序',
    `create_time`      int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`      int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`       int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY                zhanhui_guid (zhanhui_guid)
)COMMENT '商家展会常见问题表';


CREATE TABLE merchant_zhanhui_banner
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `banner_type`   tinyint unsigned NOT NULL DEFAULT 0 COMMENT '轮播图类型：1-议题轮播，2-嘉宾轮播',
    `title`         varchar(255) NOT NULL DEFAULT '' COMMENT '标题',
    `content`       varchar(600) NOT NULL DEFAULT '' COMMENT '轮播图内容',
    `image`         varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
    `jump_type`     tinyint unsigned NOT NULL DEFAULT 1 COMMENT '跳转类型：1-不跳转，2-跳转常见问题，3-播放视频',
    `jump_value`    varchar(255) NOT NULL DEFAULT '' COMMENT '跳转值：跳转常见问题时为问题guid，跳转视频时为视频地址',
    `sort`          int unsigned NOT NULL DEFAULT 0 COMMENT '排序',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY             zhanhui_guid (zhanhui_guid)
)COMMENT '商家展会轮播图表';


CREATE TABLE `merchant_zhanhui_knowledge_base`
(
    `sys_id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`            char(32)     NOT NULL COMMENT '唯一关键字段',
    `zhanhui_guid`    char(32)     NOT NULL COMMENT '展会guid',
    `dify_base_id`    varchar(255) NOT NULL DEFAULT '' COMMENT 'dify平台的id',
    `merchant_guid`   char(32)     NOT NULL COMMENT '商家guid',
    `knowledge_title` varchar(255) NOT NULL DEFAULT '' COMMENT '知识库标题',
    `create_time`     int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`     int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`      int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY               `merchant_guid` (`merchant_guid`)
) COMMENT='商家展会知识库';

CREATE TABLE `merchant_zhanhui_knowledge_base_files`
(
    `sys_id`         bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`           char(32)     NOT NULL COMMENT '唯一关键字段',
    `zhanhui_guid`   char(32)     NOT NULL COMMENT '展会guid',
    `knowledge_guid` char(32)     NOT NULL COMMENT '知识库guid',
    `dify_base_id`   varchar(255) NOT NULL DEFAULT '' COMMENT 'dify平台的知识库id',
    `dify_file_id`   varchar(255) NOT NULL DEFAULT '' COMMENT 'dify平台的文件id',
    `file_url`       varchar(255) NOT NULL DEFAULT '' COMMENT '文件地址',
    `file_name`      varchar(255) NOT NULL DEFAULT '' COMMENT '文件名称',
    `create_time`    int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`    int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`     int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (`sys_id`),
    KEY              `knowledge_guid` (`knowledge_guid`)
) COMMENT='商家知识库文件';

alter table `chat_content` add column `chat_scene` varchar(255) NOT NULL DEFAULT 'default' COMMENT '聊天场景：default-默认；zhanhui-展会聊天';
alter table `chat_content` add column `scene_value` varchar(255) NOT NULL DEFAULT '' COMMENT '聊天场景值';
# 常见问题富文本类型新增图片列表字段
alter table `merchant_zhanhui_faq` add column `image_list` json COMMENT '图片列表';
# 展会配置新增推荐企业标题
alter table `merchant_zhanhui_config` add column `recommend_company_title` varchar(255) NOT NULL DEFAULT '推荐企业' COMMENT '推荐企业标题';
# 展会新增排序字段
ALTER TABLE `merchant_zhanhui` ADD COLUMN `show_order` int(10) NOT NULL DEFAULT 1 COMMENT '展会排序';
# 展示时间新增是否展示
ALTER TABLE `merchant_zhanhui` ADD COLUMN `show_time` tinyint(1) NOT NULL DEFAULT 1 COMMENT '展示时间是否展示';

# #20241217展位小程序修改相关

# 展会管理员用户表
create table merchant_zhanhui_admin_user
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY  platform_user_sys_id (platform_user_sys_id)
)COMMENT '商家展会管理员用户表';

# 展会新增创建状态
alter table `merchant_zhanhui` add column `create_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '展会创建状态：1-创建中；2-已创建';

# 展会购买记录表
create table merchant_zhanhui_buy_record
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `zhanhui_name`  varchar(255) NOT NULL DEFAULT '' COMMENT '展会名称',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `order_no`      varchar(255) NOT NULL COMMENT '订单编号',
    `order_status`  varchar(255) NOT NULL COMMENT '订单状态：wait-等待执行；ready-准备完成；doing-正在执行；fail-执行失败；success执行成功',
    `pay_status`    tinyint unsigned NOT NULL DEFAULT 1 COMMENT '支付状态：1-未支付；2-已支付',
    `pay_amount`     int unsigned NOT NULL DEFAULT 0 COMMENT '支付金额，单位分',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id)
)COMMENT '商家展会购买记录表';

# 展会购买订单信息完善表
create table merchant_zhanhui_buy_record_info
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `fill_progress` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '内容填充进度：1-填写模块；2-上传文件；3-完成',
    `run_status`    tinyint unsigned NOT NULL DEFAULT 0 COMMENT '执行状态：0-未执行；1-执行中；2-执行完成；3-处理失败',
    `buy_record_order_no` varchar(255) NOT NULL COMMENT '购买记录订单编号',
    `modules`       json         NOT NULL COMMENT '模块信息，包含模块名称和描述',
    `knowledge_file_name` varchar(255) NOT NULL DEFAULT '' COMMENT '知识库文件名称',
    `knowledge_file_path` varchar(255) NOT NULL DEFAULT '' COMMENT '知识库文件路径',
    `xinghuo_file_id` varchar(255) NOT NULL DEFAULT '' COMMENT '星火知识库文件id',
    `theme_desc`  varchar(1000) NOT NULL DEFAULT '' COMMENT '展会主题描述',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    buy_record_order_no (buy_record_order_no)
)COMMENT '商家展会购买订单信息完善表';

# 展会新增关于展会自定义字段
ALTER TABLE `merchant_zhanhui_config` ADD COLUMN `about_zhanhui_title` text  COMMENT '关于展会标题';

# 展会留言板表
create table merchant_zhanhui_message_board
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `content`       text         NOT NULL COMMENT '留言内容',
    `note`          varchar(255) NOT NULL DEFAULT '' COMMENT '留言备注',
    `admin_is_delete` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '管理员是否删除：0-未删除；1-已删除',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',

    PRIMARY KEY (sys_id),
    KEY    platform_user_sys_id (platform_user_sys_id)
)COMMENT '商家展会留言板表';

# 留言板聊天记录
create table merchant_zhanhui_message_board_chat
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `message_board_guid` char(32)     NOT NULL COMMENT '留言板guid',
    `platform_user_sys_id` int unsigned NOT NULL  COMMENT '用户id',
    `chat_content`  text         NOT NULL COMMENT '对话内容',
    `reply_type`    tinyint unsigned NOT NULL DEFAULT 1 COMMENT '回复类型：1-用户回复；2-商家回复',
    `send_day`      int unsigned NOT NULL DEFAULT 0 COMMENT '发送日期，格式20230326',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    platform_user_sys_id (platform_user_sys_id)
)COMMENT '商家展会留言板聊天记录表';

# 展会创建兑换码表
create table merchant_zhanhui_exchange_code
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `use_zhanhui_guid`  char(32)     NOT NULL default '' COMMENT '使用展会guid',
    `use_platform_user_sys_id` int unsigned NOT NULL default 0  COMMENT '使用用户id',
    `exchange_code` varchar(255) NOT NULL COMMENT '兑换码',
    `exchange_status` tinyint unsigned NOT NULL DEFAULT 1 COMMENT '兑换码状态：1-未使用；2-已使用',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    exchange_code (exchange_code)
)COMMENT '商家展会兑换码表';

# 商家客服聊天机器人
create table merchant_customer_chat_robot
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_type`    varchar(255) NOT NULL DEFAULT '' COMMENT '机器人类型：douyin - 抖音；xiaohongshu - 小红书；weixin - 微信',
    `robot_name`    varchar(255) NOT NULL DEFAULT '' COMMENT '机器人名称',
    `robot_avatar`  varchar(255) NOT NULL DEFAULT '' COMMENT '机器人头像',
    `robot_desc`    varchar(255) NOT NULL DEFAULT '' COMMENT '机器人描述',
    `other_platform_user_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台用户uuid',
    `other_platform_user_nickname` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台用户昵称',
    `other_platform_user_avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台用户头像',
    `client_robot_name` varchar(255) NOT NULL DEFAULT '' COMMENT '客户端机器人名称',
    `extend_config` json    COMMENT '扩展配置',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    guid (guid)
)COMMENT '商家客服聊天机器人';

# 商家客服聊天机器人对话记录表
create table merchant_customer_chat_robot_record
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `other_platform_chat_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台聊天轮次uuid',
    `chat_title`   varchar(255) NOT NULL DEFAULT '' COMMENT '聊天标题',
    `chat_content` json         NOT NULL COMMENT '聊天内容',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    robot_guid (robot_guid)
)COMMENT '商家客服聊天机器人对话记录表';

# 商家客服机器人回复记录
create table merchant_customer_chat_robot_reply_record
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `other_platform_chat_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台聊天轮次uuid',
    `chat_title`   varchar(255) NOT NULL DEFAULT '' COMMENT '聊天标题',
    `ask_question`  varchar(3000)         NOT NULL default '' COMMENT '提问',
    `chat_content` json         NOT NULL COMMENT '聊天内容',
    `reply_content_type` varchar(255) NOT NULL DEFAULT '' COMMENT '回复内容类型：text-文本；image-图片；video-视频；file-文件;job-工作流ID',
    `reply_content` text         NOT NULL COMMENT '回复内容',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    robot_guid (robot_guid)
)COMMENT '商家客服机器人回复记录表';

# 商家客服机器人展会绑定记录表
create table merchant_customer_chat_robot_zhanhui
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `zhanhui_guid`  char(32)     NOT NULL COMMENT '展会guid',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    robot_guid (robot_guid)
)COMMENT '商家客服机器人展会绑定记录表';

ALTER TABLE merchant_customer_chat_robot ADD COLUMN device_id VARCHAR(255) NOT NULL DEFAULT '' COMMENT '设备ID' AFTER robot_type;

# 20240427抖音机器人完善相关

# 展会管理员新增是否为超级管理员
ALTER TABLE merchant_zhanhui_admin_user ADD COLUMN is_super_admin TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为超级管理员：0-否；1-是' AFTER platform_user_sys_id;

# 客服机器人角色表
create table merchant_customer_chat_robot_role
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `role_name`     varchar(255) NOT NULL DEFAULT '' COMMENT '角色名称',
    `role_desc`     varchar(3000) NOT NULL DEFAULT '' COMMENT '角色描述，说明当前角色用于什么场景',
    `role_prompt`   text         NOT NULL COMMENT '角色AI提示词',
    `knowledge_bind_type` varchar(255) NOT NULL DEFAULT 'default' COMMENT '知识库绑定类型：default-默认展位知识库；custom-自定义',
    `custom_knowledge_guids` json    COMMENT '自定义知识库列表',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    robot_guid (robot_guid)
) COMMENT '客服机器人角色表';

# 客服机器人任务执行表
create table merchant_customer_chat_robot_task
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `task_type`     varchar(255) NOT NULL DEFAULT 'chat_reply' COMMENT '任务类型：chat_reply-聊天回复；',
    `task_content`   text         NOT NULL COMMENT '任务内容',
    `task_params`   json  COMMENT '任务参数',
    `task_status`   tinyint unsigned NOT NULL DEFAULT 1 COMMENT '任务状态：1-未执行；2-执行中；3-执行成功；4-执行失败',
    `result_content` json  COMMENT '任务结果',
    `fail_reason`   varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
     PRIMARY KEY (sys_id),
     KEY    robot_guid (robot_guid)
) COMMENT '客服机器人任务执行表';

# 客服机器人待发送消息列表
create table merchant_customer_chat_robot_send_message
(
    `sys_id`        bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键字段',
    `guid`          char(32)     NOT NULL COMMENT '唯一关键字段',
    `merchant_guid` char(32)     NOT NULL COMMENT '商家guid',
    `robot_guid`    char(32)     NOT NULL COMMENT '机器人guid',
    `other_platform_chat_uuid` varchar(255) NOT NULL DEFAULT '' COMMENT '第三方平台聊天轮次uuid',
    `content_type` varchar(255) NOT NULL DEFAULT '' COMMENT '内容类型：text-文本；image-图片；video-视频；file-文件；job-工作流ID',
    `content`   text         NOT NULL COMMENT '内容',
    `create_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '创建时间戳',
    `update_time`   int unsigned NOT NULL DEFAULT 0 COMMENT '修改时间戳',
    `deleted_at`    int unsigned DEFAULT NULL COMMENT '删除时间',
    `modify_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
    PRIMARY KEY (sys_id),
    KEY    robot_guid (robot_guid)
) COMMENT '客服机器人待发送消息列表';